// src/email.js
import { jwtVerify } from 'jose';
import TEMPLATE_HTML from './template.html';

async function isAuthenticated(request, env) {
  const authHeader = request.headers.get('Authorization') || '';
  const token = authHeader.replace('Bearer ', '');
  if (!token) return false;
  try {
    const secret = new TextEncoder().encode(env.JWT_SECRET);
    await jwtVerify(token, secret);
    return true;
  } catch {
    return false;
  }
}

export default async function sendEmail(context) {
  if (!(await isAuthenticated(context.request, context.env))) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
  }

  const data = await context.request.json();

  let subject;
  let htmlContent;

  if (data.template) {
    subject = "Helping Schools Shine Online";
    htmlContent = TEMPLATE_HTML;
  } else {
    subject = data.subject;
    htmlContent = data.htmlContent;
    if (!subject || !htmlContent) {
      return new Response(JSON.stringify({ error: 'Missing subject or content' }), { status: 400 });
    }
  }

  const payload = {
    sender: { name: context.env.SENDER_NAME, email: context.env.SENDER_EMAIL },
    to: data.to.map(email => ({ email })),
    subject,
    htmlContent
  };

  const brevoRes = await fetch("https://api.brevo.com/v3/smtp/email", {
    method: "POST",
    headers: {
      "api-key": context.env.BREVO_API_KEY,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(payload)
  });

  const resText = await brevoRes.text();
  let resBody;
  try {
    resBody = JSON.parse(resText);
  } catch {
    resBody = { message: resText };
  }
  return new Response(JSON.stringify(resBody), {
    status: brevoRes.status,
    headers: { "Content-Type": "application/json" }
  });
}

# wrangler.toml
name = "mail"
main = "src/index.js"
compatibility_date = "2024-07-07"
account_id = "991b075f62d8e8bd313a8cfe6af58945"
compatibility_flags = [ "nodejs_compat" ]

[vars]
LOGIN_EMAIL = "<EMAIL>"
LOGIN_PASSWORD = "rajib99"
BREVO_API_KEY = "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"
JWT_SECRET = "oqbeouqpcrnc0920wkjh7308b0y9246r9och03r10n"
SENDER_NAME = "Lets GenAi"
SENDER_EMAIL = "<EMAIL>"

[[rules]]
type = "Text"
globs = ["**/*.html"]

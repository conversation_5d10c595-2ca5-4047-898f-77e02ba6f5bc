<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Offer Letter</title>
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      font-family: Arial, sans-serif;
      color: #333;
      box-sizing: border-box;
    }

    .container {
      max-width: 600px;
      background: #fff;
      padding: 30px;
      border-radius: 4px;
      border: 2px solid #007bff;
      margin: 20px auto; /* keeps some white space */
      box-sizing: border-box;
    }

    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .header img {
      width: 60px;
      max-width: 60%;
      height: auto;
    }
    h2 {
      color: #0056b3;
      text-align: center;
      margin-bottom: 30px;
      font-size: 1.5rem;
    }
    .info {
      margin-bottom: 20px;
    }
    .info strong {
      color: #000;
    }
    .content p {
      line-height: 1.6;
      margin-bottom: 15px;
    }
    .signature {
      margin-top: 30px;
      font-weight: bold;
    }
    .highlight {
      color: #0056b3;
      font-weight: bold;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      html, body {
        height: auto;
        min-height: 100%;
        padding: 0;
        margin: 0;
      }

      .container {
        width: calc(100% - 30px); /* keep small white space */
        max-width: 100%;
        padding: 20px;
        margin: 20px auto;
        border-radius: 4px;
        box-sizing: border-box;
      }

      h2 {
        font-size: 1.3rem;
        margin-bottom: 20px;
      }
      .header img {
        width: 80px;
      }
      .content p {
        font-size: 0.95rem;
      }
      .info {
        margin-bottom: 15px;
      }
    }

    @media (max-width: 480px) {
      .container {
        width: calc(100% - 20px); /* minimal margin */
        padding: 15px;
        margin: 10px auto 20px auto; /* extra bottom margin so blue border is visible */
        box-sizing: border-box;
      }
      h2 {
        font-size: 1.2rem;
      }
      .header img {
        width: 70px;
      }
      .content p {
        font-size: 0.9rem;
        line-height: 1.5;
      }
      .signature {
        margin-top: 20px;
        font-size: 0.9rem;
      }
    }

    /* Ensure body has enough padding to show bottom border */
    body::after {
      content: "";
      display: block;
      height: 20px; /* reserve space at bottom */
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="logo.png" alt="Lets GenAi Logo" />
      <h2>Offer Letter</h2>
    </div>

    <div class="info">
      <p><strong>{candidate_name}</strong><br>
      {candidate_address}<br>
      {candidate_email}<br>
      {candidate_phone}<br>
      <span class="highlight">{offer_date}</span></p>
    </div>

    <div class="info">
      <p><strong>Dear {candidate_name},</strong></p>
    </div>

    <div class="content">
      <p>We are pleased to offer you the position of <span class="highlight">{job_title}</span> at <strong>Lets GenAi</strong>. Your joining date will be <span class="highlight">{joining_date}</span> or any mutually agreed date.</p>

      <p>Your total compensation will be <strong>{salary_details}</strong> as discussed. You will be working from home.</p>

      <p>Please review the attached document for detailed terms and conditions of your employment. Kindly confirm your acceptance by replying to this email or signing and returning the attached offer copy.</p>

      <p>We are excited to have you join our team and look forward to a successful journey together.</p>

      <p class="signature">Sincerely,<br>
      {hr_name}<br>
      Lets GenAi</p>
    </div>
  </div>
</body>
</html>

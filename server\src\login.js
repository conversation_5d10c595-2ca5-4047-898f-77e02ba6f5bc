// src/login.js
import { SignJWT } from 'jose';

export default async function login(context) {
  const { email, password } = await context.request.json();

  if (email === context.env.LOGIN_EMAIL && password === context.env.LOGIN_PASSWORD) {
    const secret = new TextEncoder().encode(context.env.JWT_SECRET);
    const jwt = await new SignJWT({ email })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1h')
      .sign(secret);

    return new Response(JSON.stringify({ token: jwt }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } else {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
  }
}
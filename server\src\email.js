// src/email.js
import { jwtVerify } from 'jose';
import SCHOOL_TEMPLATE from './school.html';
import OFFER_TEMPLATE from './offer.html';

async function isAuthenticated(request, env) {
  const authHeader = request.headers.get('Authorization') || '';
  const token = authHeader.replace('Bearer ', '');
  if (!token) return false;
  try {
    const secret = new TextEncoder().encode(env.JWT_SECRET);
    await jwtVerify(token, secret);
    return true;
  } catch {
    return false;
  }
}

export default async function sendEmail(context) {
  if (!(await isAuthenticated(context.request, context.env))) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
  }

  const data = await context.request.json();

  let subject;
  let htmlContent;

  if (data.template && data.template !== 'none') {
    if (data.template === 'school') {
      subject = "Helping Schools Shine Online";
      htmlContent = SCHOOL_TEMPLATE;
    } else if (data.template === 'offer') {
      subject = "Job Offer Letter";
      htmlContent = OFFER_TEMPLATE;

      // Replace placeholders with actual data for offer template
      if (data.templateData) {
        const replacements = {
          '{candidate_name}': data.templateData.candidateName || '',
          '{candidate_address}': data.templateData.candidateAddress || '',
          '{candidate_email}': data.templateData.candidateEmail || '',
          '{candidate_phone}': data.templateData.candidatePhone || '',
          '{offer_date}': data.templateData.offerDate || '',
          '{job_title}': data.templateData.jobTitle || '',
          '{company_name}': data.templateData.companyName || '',
          '{joining_date}': data.templateData.joiningDate || '',
          '{salary_details}': data.templateData.salaryDetails || '',
          '{reporting_manager}': data.templateData.reportingManager || '',
          '{office_location}': data.templateData.officeLocation || '',
          '{hr_name}': data.templateData.hrName || '',
          '{hr_designation}': data.templateData.hrDesignation || ''
        };

        for (const [placeholder, value] of Object.entries(replacements)) {
          htmlContent = htmlContent.replace(new RegExp(placeholder, 'g'), value);
        }
      }
    } else {
      return new Response(JSON.stringify({ error: 'Invalid template type' }), { status: 400 });
    }
  } else {
    subject = data.subject;
    htmlContent = data.htmlContent;
    if (!subject || !htmlContent) {
      return new Response(JSON.stringify({ error: 'Missing subject or content' }), { status: 400 });
    }
  }

  const payload = {
    sender: { name: context.env.SENDER_NAME, email: context.env.SENDER_EMAIL },
    to: data.to.map(email => ({ email })),
    subject,
    htmlContent
  };

  const brevoRes = await fetch("https://api.brevo.com/v3/smtp/email", {
    method: "POST",
    headers: {
      "api-key": context.env.BREVO_API_KEY,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(payload)
  });

  const resText = await brevoRes.text();
  let resBody;
  try {
    resBody = JSON.parse(resText);
  } catch {
    resBody = { message: resText };
  }
  return new Response(JSON.stringify(resBody), {
    status: brevoRes.status,
    headers: { "Content-Type": "application/json" }
  });
}

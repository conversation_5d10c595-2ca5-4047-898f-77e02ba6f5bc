// src/App.jsx
import React, { useState, useEffect } from 'react';
import jsPDF from 'jspdf';
import { LogOut, Send, FileText } from 'lucide-react';

const EMAIL_HISTORY_KEY = 'sent_emails';
const API_BASE = "https://mail.rb13190606.workers.dev";

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(() => !!localStorage.getItem('jwt_token'));
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailData, setEmailData] = useState({
    to: '',
    subject: '',
    htmlContent: ''
  });
  const [selectedTemplate, setSelectedTemplate] = useState('none');
  const [templateData, setTemplateData] = useState({
    candidateName: '',
    candidateAddress: '',
    candidateEmail: '',
    candidatePhone: '',
    offerDate: '',
    jobTitle: '',
    joiningDate: '',
    salaryDetails: '',
    hrName: ''
  });
  const [history, setHistory] = useState([]);
  const [token, setToken] = useState(() => localStorage.getItem('jwt_token'));

  useEffect(() => {
    const storedEmails = localStorage.getItem(EMAIL_HISTORY_KEY);
    if (storedEmails) setHistory(JSON.parse(storedEmails));
  }, []);

  const handleLogin = async () => {
    try {
      const res = await fetch(`${API_BASE}/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      });

      if (res.ok) {
        const data = await res.json();
        setToken(data.token);
        setIsAuthenticated(true);
        localStorage.setItem('jwt_token', data.token);
      } else {
        alert('Login failed');
      }
    } catch (err) {
      alert('Login error');
    }
  };

  const handleSendEmail = async () => {
    try {
      const res = await fetch(`${API_BASE}/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          to: emailData.to.split(',').map(e => e.trim()),
          ...(selectedTemplate !== 'none' ? {
            template: selectedTemplate,
            ...(selectedTemplate === 'offer' ? { templateData } : {})
          } : {
            subject: emailData.subject,
            htmlContent: emailData.htmlContent
          })
        })
      });

      if (res.ok) {
        const updated = [...history, emailData];
        localStorage.setItem(EMAIL_HISTORY_KEY, JSON.stringify(updated));
        setHistory(updated);
        alert('Email sent successfully');
      } else {
        let errMsg = 'Unknown error';
        try {
          const err = await res.json();
          errMsg = err.error || JSON.stringify(err);
        } catch {
          errMsg = await res.text();
        }
        alert('Send failed: ' + errMsg);
      }
    } catch (err) {
      alert('Failed to send email: ' + err.message);
    }
  };

  const handleLogout = () => {
    const doc = new jsPDF();
    doc.setFontSize(12);
    let y = 10;
    history.forEach((email, i) => {
      doc.text(`Email ${i + 1}:`, 10, y);
      y += 6;
      Object.entries(email).forEach(([key, value]) => {
        doc.text(`${key}: ${value}`, 12, y);
        y += 6;
      });
      y += 4;
    });
    doc.save('sent_emails.pdf');

    localStorage.removeItem(EMAIL_HISTORY_KEY);
    localStorage.removeItem('jwt_token');
    setIsAuthenticated(false);
    setHistory([]);
    setEmail('');
    setPassword('');
    setToken(null);
  };

  const handleChange = field => e => {
    setEmailData({ ...emailData, [field]: e.target.value });
  };

  const handleTemplateDataChange = field => e => {
    setTemplateData({ ...templateData, [field]: e.target.value });
  };

  if (!isAuthenticated) {
    return (
      <div className="container">
        <h2>Login to Send Emails</h2>
        <input placeholder="Email" value={email} onChange={e => setEmail(e.target.value)} />
        <input type="password" placeholder="Password" value={password} onChange={e => setPassword(e.target.value)} />
        <button onClick={handleLogin}>Login</button>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="header">
        <h2>Email Client</h2>
        <button onClick={handleLogout}><LogOut size={16} style={{ marginRight: 4 }} /> Logout</button>
      </div>
      <input placeholder="To" value={emailData.to} onChange={handleChange('to')} />
      <div style={{ marginBottom: '10px' }}>
        <label>Template:</label>
        <select value={selectedTemplate} onChange={e => setSelectedTemplate(e.target.value)} style={{ marginLeft: '10px', padding: '5px' }}>
          <option value="none">None (Custom)</option>
          <option value="school">School Template</option>
          <option value="offer">Offer Letter Template</option>
        </select>
      </div>

      {selectedTemplate === 'none' && (
        <>
          <input placeholder="Subject" value={emailData.subject} onChange={handleChange('subject')} />
          <textarea placeholder="HTML Content" rows={8} value={emailData.htmlContent} onChange={handleChange('htmlContent')} />
        </>
      )}

      {selectedTemplate === 'offer' && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Offer Letter Details</h3>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
            <input placeholder="Candidate Name" value={templateData.candidateName} onChange={handleTemplateDataChange('candidateName')} />
            <input placeholder="Candidate Email" value={templateData.candidateEmail} onChange={handleTemplateDataChange('candidateEmail')} />
            <input placeholder="Candidate Phone" value={templateData.candidatePhone} onChange={handleTemplateDataChange('candidatePhone')} />
            <input placeholder="Offer Date" value={templateData.offerDate} onChange={handleTemplateDataChange('offerDate')} />
            <input placeholder="Job Title" value={templateData.jobTitle} onChange={handleTemplateDataChange('jobTitle')} />
            <input placeholder="Joining Date" value={templateData.joiningDate} onChange={handleTemplateDataChange('joiningDate')} />
            <input placeholder="Salary Details" value={templateData.salaryDetails} onChange={handleTemplateDataChange('salaryDetails')} />
            <input placeholder="HR Name" value={templateData.hrName} onChange={handleTemplateDataChange('hrName')} />
          </div>
          <textarea
            placeholder="Candidate Address"
            rows={3}
            value={templateData.candidateAddress}
            onChange={handleTemplateDataChange('candidateAddress')}
            style={{ marginTop: '10px' }}
          />
        </div>
      )}
      <button onClick={handleSendEmail}><Send size={16} style={{ marginRight: 4 }} /> Send Email</button>
    </div>
  );
};

export default App;

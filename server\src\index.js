// src/index.js
import login from './login';
import sendEmail from './email';

const ALLOWED_ORIGIN = 'https://mail.letsgenai.in';

function getCorsHeaders(request) {
  const origin = request.headers.get('Origin');
  const isAllowed = origin === ALLOWED_ORIGIN;

  return {
    'Access-Control-Allow-Origin': isAllowed ? origin : 'null',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  };
}

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const corsHeaders = getCorsHeaders(request);

    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 204, headers: corsHeaders });
    }

    let response;
    if (request.method === 'POST' && url.pathname === '/login') {
      response = await login({ request, env, ctx });
    } else if (request.method === 'POST' && url.pathname === '/') {
      response = await sendEmail({ request, env, ctx });
    } else {
      response = new Response('Not Found', { status: 404 });
    }

    const finalHeaders = new Headers(response.headers);
    for (const [k, v] of Object.entries(corsHeaders)) {
      finalHeaders.set(k, v);
    }

    return new Response(response.body, {
      status: response.status,
      headers: finalHeaders
    });
  }
};
